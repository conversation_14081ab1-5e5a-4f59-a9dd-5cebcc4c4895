---
variables:
    GIT_STRATEGY: clone
    MAVEN_OPTS: "${MAVEN_PROXY_OPTS}"
    
stages:
    - build
    - deploy_infra
    - deploy_preproduction
    - deploy_PRODUCTION


docker_image:
    stage: build
    image: registry.eit.zone/loxon/devops/sas-gradle:1.0.4
    script:
        - chmod 755 gradlew
        - export CUSTOM_TIME=$(date +"%Y%m%d_%H%M%S")
        - ln -s /opt/gradle/gradle-bin.zip  gradle/wrapper/
        - ./gradlew ${MAVEN_PROXY_OPTS} --no-daemon build -x test 

    artifacts:
      paths:
        - ./build/libs/*jar

    tags:
        - cz-small
#        - drc
#    rules:
#        - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#        - changes:
#              paths:
#                  - src

.app_deplyoment:
  image:
    name: registry.eit.zone/loxon/devops/loxon-ansible:1.0.10
    entrypoint: [""]
  script:
    - echo "deploying app via ansible"
    - eval `ssh-agent -s`
    - ssh-add - <<< "${SAS_SVC_key}"
    - echo "ansible  running"
    - git clone https://oauth2:$<EMAIL>/SAS.Deployments/ansible/inventory.git
    - ansible-playbook -i inventory/${country_code^^}/${country_code^^}_${env_zone}.yml deployment_playbook.yml --limit "${limit_regex}"
  dependencies:
    - docker_image
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
    - if: '$CI_PIPELINE_SOURCE == "web"'
    
.app_deplyoment-vn:
  extends: .app_deplyoment
  tags:
    - vn-small
  variables:
    country_code: VN
    env_zone: infra

.app_deplyoment-id:
  extends: .app_deplyoment
  tags:
    - id-small
  variables:
    country_code: ID
    env_zone: infra

.app_deplyoment-ph:
  extends: .app_deplyoment
  tags:
    - ph-small
  variables:
    country_code: PH
    env_zone: infra

.app_deplyoment-in:
  extends: .app_deplyoment
  tags:
    - in-small
  variables:
    country_code: IN
    env_zone: infra


"PH deplyoment dev":
  extends: .app_deplyoment-ph
  stage: deploy_infra
  variables:
    limit_regex: sas03-ph00d1*,sas02-ph00d1*

"ID deplyoment dev":
  extends: .app_deplyoment-id
  stage: deploy_infra
  variables:
    limit_regex: sas03-id00d1*,sas02-id00d1*

"IN deplyoment dev":
  extends: .app_deplyoment-in
  stage: deploy_infra
  variables:
    limit_regex: sas03-in00d1*,sas02-in00d1*

"VN deplyoment dev":
  extends: .app_deplyoment-vn
  stage: deploy_infra
  variables:
    limit_regex: sas03-vn00d1*,sas02-vn00d1*

deploy_test-vn:
  stage: deploy_infra
  when: manual
  allow_failure: false
  extends: .app_deplyoment-vn
  variables:
    limit_regex: sas03-vn00c1*,sas02-vn00c1*

deploy_test-ph:
  stage: deploy_infra
  when: manual
  allow_failure: false
  extends: .app_deplyoment-ph
  variables:
    limit_regex: sas03-ph00c1*,sas02-ph00c1*

deploy_test-in:
  stage: deploy_infra
  when: manual
  allow_failure: false
  extends: .app_deplyoment-in
  variables:
    limit_regex: sas03-in00c1*,sas02-in00c1*

deploy_test-id:
  stage: deploy_infra
  when: manual
  allow_failure: false
  extends: .app_deplyoment-id
  variables:
    limit_regex: sas03-id00c1*,sas02-id00c1*
    
preproduction-vn:
  stage: deploy_preproduction
  when: manual
  extends: .app_deplyoment-vn
  needs: ["deploy_test-vn","docker_image"]
  variables:
    limit_regex: sas03-vn01p*,sas02-vn01p*
    country_code: VN
    env_zone: nonprod    

preproduction-ph:
  stage: deploy_preproduction
  when: manual
  extends: .app_deplyoment-ph
  needs: ["deploy_test-ph","docker_image"]
  variables:
    limit_regex: sas03-ph01p*,sas02-ph01p*
    country_code: PH
    env_zone: nonprod    

preproduction-in:
  stage: deploy_preproduction
  when: manual
  extends: .app_deplyoment-in
  needs: ["deploy_test-in","docker_image"]
  variables:
    limit_regex: sas03-in01p*,sas02-in01p*
    country_code: IN
    env_zone: nonprod    

preproduction-id:
  stage: deploy_preproduction
  when: manual
  extends: .app_deplyoment-id
  needs: ["deploy_test-id","docker_image"]
  variables:
    limit_regex: sas03-id02p*,sas02-id02p*
    country_code: ID
    env_zone: nonprod    


PRODUCTION-ID:
  stage: deploy_PRODUCTION
  when: manual
  extends: .app_deplyoment-id
  needs: ["preproduction-id","docker_image"]
  variables:
    limit_regex: sas03-pdcid1*,sas02-pdcid1*
    country_code: ID
    env_zone: prod    

PRODUCTION-VN:
  stage: deploy_PRODUCTION
  when: manual
  extends: .app_deplyoment-vn
  needs: ["preproduction-vn","docker_image"]
  variables:
    limit_regex: sas03-pdcvn1*,sas02-pdcvn1*
    country_code: VN
    env_zone: prod    

PRODUCTION-PH:
  stage: deploy_PRODUCTION
  when: manual
  extends: .app_deplyoment-ph
  needs: ["preproduction-ph","docker_image"]
  variables:
    limit_regex: sas03-pdcph1 *,sas02-pdcph1*
    country_code: PH
    env_zone: prod    

PRODUCTION-IN:
  stage: deploy_PRODUCTION
  when: manual
  extends: .app_deplyoment-in
  needs: ["preproduction-in","docker_image"]
  variables:
    limit_regex: sas03prod*,sas02prod*
    country_code: IN
    env_zone: prod
