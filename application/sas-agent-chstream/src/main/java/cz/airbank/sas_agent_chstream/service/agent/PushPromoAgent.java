package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.push.SendPushPromoEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.kafka.producer.SendPushPromoEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendPushPromoEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.MaPushEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import cz.airbank.sas_agent_chstream.service.Agent;
import cz.airbank.sas_agent_chstream.validator.AvroValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Map;

import static cz.airbank.sas_agent_chstream.util.CreativeContentUtil.parseCreativeContent;

@Service
@Slf4j
@RequiredArgsConstructor
public class PushPromoAgent implements Agent {

    private final SendPushPromoEventKafkaProducer producer;
    private final TaskCache taskCache;
    private final ObjectMapper objectMapper;
    private final SasRepository sasRepository;
    private final SendPushPromoEventMapper mapper;

    @Override
    public AgentType getAgentType() {
        return AgentType.PUSH_SEND;
    }

    @Override
    public void processMessage(String event) {
        MaPushEvent maPushEvent;
        try {
            maPushEvent = objectMapper.readValue(event, MaPushEvent.class);
        } catch (Exception e) {
            log.error("Failed to parse event json into MaPushEvent object", e);
            return;
        }

        String taskId = maPushEvent.attributes().taskId();
        String taskVersionId = maPushEvent.attributes().taskVersionId();

        TaskCacheEntry taskCacheEntry = taskCache.getTask(taskVersionId, taskId);
        if (taskCacheEntry == null) {
            log.warn("Failed to retrieve task cache for taskVersionId {} and taskId {}. Stop processing event", taskVersionId, taskId);
            return;
        }

        Map<String, String> creativeContent = parseCreativeContent(maPushEvent.attributes().creativeContent());
        String campaignName = sasRepository.getCampaignName(taskCacheEntry.tsk_comm_camp_name.value);
        String leadId = getLeadId(
                maPushEvent.attributes().datahubId(),
                maPushEvent.attributes().responseTrackingCode(),
                maPushEvent.attributes().timestamp());

        SendPushPromoEvent pushPromoEvent;
        try {
            pushPromoEvent = mapper.toSendPushPromoEvent(maPushEvent.attributes(), creativeContent, taskCacheEntry, leadId, campaignName);
            AvroValidator.validateNonNullableFields(pushPromoEvent);
        } catch (CHStreamAgentException e) {
            sasRepository.storeErrorToDb(leadId, taskCacheEntry.tsk_comm_chan_code.value, maPushEvent.attributes().timestamp(), e.getMessage());
            return;
        }

        producer.publish(pushPromoEvent.getCuid(), pushPromoEvent);
    }

    @Override
    public void processMessage(JSONObject event) throws Exception {
        throw new UnsupportedOperationException("Use processMessage(String event)");
    }
}
