<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           attributeFormDefault="unqualified"
           elementFormDefault="qualified"
           targetNamespace="http://airbank.cz/ws/card/discountprogram"
           xmlns="http://airbank.cz/ws/card/discountprogram">


    <xs:element name="isCustomerEligibleRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cuid" nillable="true" type="xs:long"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="isCustomerEligibleResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="eligible" type="xs:boolean"/>
                <xs:element name="inegibilityReason" minOccurs="0">
                    <xs:annotation>
                        <xs:documentation>Only if eligible = false describe reason for inegibilityReason </xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="PROCESSING_OBJECTION">
                                <xs:annotation>
                                    <xs:documentation>Customer dont want use discount program</xs:documentation>
                                </xs:annotation>
                            </xs:enumeration>
                            <xs:enumeration value="NO_CARD">
                                <xs:annotation>
                                    <xs:documentation>Customer havent card in valid state</xs:documentation>
                                </xs:annotation>
                            </xs:enumeration>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>