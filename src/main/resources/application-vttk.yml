logging:
  level:
    ROOT: INFO
    com.glowbyte: TRACE

tiktok:
  access-token: 055bb0e5d1fbdad41cfdf252e8b39087db69bccb
  advertiser-id: 7473396908854198288

hc:
  input:
    procedure: MA_TEMP.CORE_PCG.GET_COMMUNICATION_TABLE
    batch-size: 100
    max-iterations: 1
  output.procedure: MA_TEMP.CORE_PCG.INSERT_INTO_MIL

  ads:
    select-unfinished-jobs: SELECT JOB_ID FROM {}
    update-unfinished-jobs: UPDATE {} SET JOB_STATUS = :JOB_STATUS, TBU_FLAG = 'Y', UPDATED_DTTM = SYSTIMESTAMP WHERE JOB_ID = :JOB_ID AND JOB_STATUS <> :JOB_STATUS

    area-code: 84

spring:
  main:
    web-application-type: none
