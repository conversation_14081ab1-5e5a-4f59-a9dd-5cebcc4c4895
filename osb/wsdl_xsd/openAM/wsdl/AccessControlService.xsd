<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xsd:schema targetNamespace="http://osb.banka.hci/AccessControl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://osb.banka.hci/AccessControl/"
	xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<xsd:element name="authenticateRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="username" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation>client username</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="password" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation>client password in plain format</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="authenticateResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="result" type="tns:resultType" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>OK, if authentication succeeded, fault message otherwise.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="role" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
					<xsd:annotation>
						<xsd:documentation>list of user roles</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="employeeNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>employee number</xsd:documentation>
					</xsd:annotation>
				</xsd:element>								
				<xsd:element name="givenName" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>user given name</xsd:documentation>
					</xsd:annotation>
				</xsd:element>								
				<xsd:element name="surname" type="xsd:string" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>users surname</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="tokenId" type="xsd:string" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>token id</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="isEmployeeCustomerRequest">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="employeeNumber" type="xsd:string">
					<xsd:annotation>
						<xsd:documentation>employee number</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="cuid" type="xsd:long">
					<xsd:annotation>
						<xsd:documentation>client user id (CIF)</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="isEmployeeCustomerResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="result" type="xsd:boolean" minOccurs="1" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>true, if customer is employee, false otherwise.</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getEmployeeRequest">
		<xsd:complexType>
			<xsd:choice>
				<xsd:element name="employeeNumber" type="xsd:string" maxOccurs="1" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>employee number</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
				<xsd:element name="uid" type="xsd:long" maxOccurs="1" minOccurs="0">
					<xsd:annotation>
						<xsd:documentation>client user id (CIF)</xsd:documentation>
					</xsd:annotation>
				</xsd:element>
			</xsd:choice>
		</xsd:complexType>
	</xsd:element>
	<xsd:element name="getEmployeeResponse">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="employee" type="tns:Employee" minOccurs="0" maxOccurs="1">
					<xsd:annotation>
						<xsd:documentation>Employee data</xsd:documentation>
					</xsd:annotation>
				</xsd:element>

			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
        
	<xsd:complexType name="Employee">
              <xsd:sequence>
			<xsd:element name="employeeNumber" type="xsd:string"/>
			<xsd:element name="uid" type="xsd:string"/>
			<xsd:element name="givenName" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="sureName" type="xsd:string" minOccurs="1" maxOccurs="unbounded"/>
			<xsd:element name="displayName" type="xsd:string" minOccurs="1" maxOccurs="unbounded"/>
			<xsd:element name="mail" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="mobile" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="phone" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="employeeType" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="employedSince" type="xsd:dateTime" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="employyPosition" type="xsd:string" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="department" type="xsd:string" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="status" type="xsd:string" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="blockedBySecurity" type="xsd:string" minOccurs="0" maxOccurs="1"/>
              </xsd:sequence>
	</xsd:complexType>
        
	<xsd:simpleType name="resultType">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="OK">
				<xsd:annotation>
					<xsd:documentation>authentication succeeded. result should have list of roles </xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
			<xsd:enumeration value="AUTH_FAILED">
				<xsd:annotation>
					<xsd:documentation>Authentication failed for any reason</xsd:documentation>
				</xsd:annotation>
			</xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>