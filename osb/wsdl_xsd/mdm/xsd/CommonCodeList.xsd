<?xml version="1.0" encoding="UTF-8"?>
<!-- $Id: 6a7f38d5cc32a8322b60d50a47b2f40b778aa7be $ -->
<xs:schema targetNamespace="http://osb.banka.hci/CodeList/Notification/data" elementFormDefault="qualified"
	xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:complexType name="CodeListItem">
		<xs:sequence>
			<xs:element name="code" type="xs:string">
				<xs:annotation>
					<xs:documentation>single code of current codeList. In MDM is presented as businessKey</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="description" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>business description of code item. </xs:documentation>
				</xs:annotation>
			</xs:element>
            <xs:element name="timestamp" type="xs:dateTime" maxOccurs="1" minOccurs="1">
                <xs:annotation>
                    <xs:documentation>time of last modification</xs:documentation>
                </xs:annotation>
            </xs:element>			
			<xs:element name="attribute" maxOccurs="unbounded" minOccurs="0">
				<xs:annotation>
					<xs:documentation>list of attributes for code item. i.e. ordinal number</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="name" use="required"/>
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
